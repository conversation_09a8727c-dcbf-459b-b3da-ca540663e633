package com.leave.ink;

import YeQing.ClassObfuscator;
import com.external.ui.ExternalUI;
import com.external.ui.UISystem;
import com.leave.ink.core.UserInfo;
import com.leave.ink.features.config.ConfigManager;
import com.leave.ink.core.LaunchMode;
import com.leave.ink.features.command.CommandManager;
import com.leave.ink.features.hud.HudManager;
import com.leave.ink.features.hud.dynamicIsland.impl.tab.TabOverlayManager;
import com.leave.ink.features.module.ModuleManager;
import com.leave.ink.features.friend.FriendsManager;
import com.leave.ink.natives.AgentNative;
import com.leave.ink.ui.notification.NotificationManager;
import com.leave.ink.ui.skija.SkiaProcess;
import com.leave.ink.ui.skija.fbo.FrameBuffers;
import com.leave.ink.injection.TransformerLoader;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.client.CommonEvents;
import com.leave.ink.utils.fonts.FontRenderers;
import com.leave.ink.utils.manager.SprintManager;
import com.leave.ink.utils.manager.HeypixelManager;
import com.leave.ink.utils.render.Projection;
import com.leave.ink.utils.render.RenderUtils;
import com.leave.ink.utils.rotation.RotationUtils;
import com.mojang.blaze3d.systems.RenderSystem;
import net.minecraft.client.Minecraft;
import net.minecraftforge.fml.ModList;
import net.minecraftforge.forgespi.language.IModFileInfo;
import net.minecraftforge.forgespi.language.IModInfo;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

@ClassObfuscator
public class Main {
    public static Main INSTANCE = new Main();
    public static String NAME = "LEAVE";
    public static final String VERSION = "2.1 Test";
    public TransformerLoader transformerLoader;
    public LaunchMode launchMode;
    public NotificationManager notificationManager;
    public TabOverlayManager tabOverlayManager;
    public CommandManager commandManager;
    public FriendsManager friendsManager;
    public ConfigManager configManager;
    public ModuleManager moduleManager;
    public HudManager hudManager;
    public Projection projection;
    public static Thread SkijaThread;
    public SkiaProcess skiaProcess;
    public static String GPU;
    public SprintManager sprintManager;
    public Minecraft minecraft;
    private String hwid = null;

    public void run(LaunchMode launchMode) {
        this.launchMode = launchMode;
        CountDownLatch latch = new CountDownLatch(1);
        minecraft = get_mc_Instance();
        RenderSystem.recordRenderCall(() -> {
            System.setProperty("skija.library.path", Utils.getMainPath() + "\\lib");
            skiaProcess = new SkiaProcess();
            System.out.println("Skija Init!");
            latch.countDown();
        });
        System.out.println("MainThread Init");
        init();
    }

    public void init() {
        //String str = AgentNative.get(VERSION);
        // if (!str.contains("OK_"))
        // {
        // return;
        // }

        if (!AgentNative.startup(true)) {
            System.out.println("Load");
            return;
        }

        String token = ExternalUI.getValidToken();

        if (token == null) {
            ExternalUI.log("INVALID TOKEN");
            System.out.println("invalid Token!!!");
            return;
        }

        FrameBuffers.register("Inventory");
        FrameBuffers.register("Test");
        FrameBuffers.register("TabOverlay");
        FrameBuffers.register("Inventory_Item_Fallback");
        FrameBuffers.register("NameTags_Armor");

        sprintManager = new SprintManager();
        GPU = ExternalUI.getGpuName();
        transformerLoader = new TransformerLoader();
        configManager = new ConfigManager();
        notificationManager = new NotificationManager();
        tabOverlayManager = new TabOverlayManager();
        projection = new Projection();
        commandManager = new CommandManager();
        moduleManager = new ModuleManager();
        hudManager = new HudManager();
        hudManager.init();
        SkijaThread = new Thread(UISystem::UI_Initialize);
        SkijaThread.start();
        transformerLoader.redefineClass();
        friendsManager = new FriendsManager();
        UserInfo.getUserInfo(token);
        //HeartbeatThread.startThread(300);
        new CommonEvents();
        new RenderUtils();
        new HeypixelManager();
        new RotationUtils();
        loadFont();
        if (!UserInfo.userInfo.hwid().equals(hwid)) {
            System.out.println("1FFFFF");
//            UnsafeUtils.unsafe.freeMemory(Long.MAX_VALUE);
//            UnsafeUtils.unsafe.freeMemory(1111114541L);
//            UnsafeUtils.unsafe.freeMemory(1919810000L);
//            UnsafeUtils.unsafe.freeMemory(198964848488L);
//            UnsafeUtils.unsafe.freeMemory(1111114541L);
//            UnsafeUtils.unsafe.freeMemory(1919810000L);
//            UnsafeUtils.unsafe.freeMemory(198964848488L);
//            System.exit(0);
        }

        if (UserInfo.userInfo.is("Admin")) {
            System.out.println("Welcome, developers.");
        }

        removeModInfo();
    }

    public void removeModInfo() {
        ModList.get().getMods().removeIf(modInfo -> modInfo.getModId().equals("examplemod"));

        List<IModFileInfo> fileInfoToRemove = new ArrayList<>();
        for (IModFileInfo fileInfo : ModList.get().getModFiles()) {
            for (IModInfo modInfo : fileInfo.getMods()) {
                if (modInfo.getModId().equals("examplemod")) {
                    fileInfoToRemove.add(fileInfo);
                }
            }
        }

        ModList.get().getModFiles().removeAll(fileInfoToRemove);
    }

    private Minecraft get_mc_Instance() {
        Minecraft minecraft = null;
        try {
            Class<?> classMinecraft = Class.forName("net.minecraft.client.Minecraft");
            for (Field field : classMinecraft.getDeclaredFields()) {
                if (field.getType() == classMinecraft) {
                    field.setAccessible(true);
                    minecraft = (Minecraft) field.get(null);
                    field.setAccessible(false);
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return minecraft;
    }

    private void loadFont() {
        try {
            FontRenderers.settings = FontRenderers.create(12f, "comfortaa");
            FontRenderers.modules = FontRenderers.create(15f, "comfortaa");
            FontRenderers.categories = FontRenderers.create(18f, "comfortaa");
            FontRenderers.thglitch = FontRenderers.create(36f, "glitched");
            FontRenderers.thglitchBig = FontRenderers.create(72f, "glitched");
            FontRenderers.monsterrat = FontRenderers.create(18f, "monsterrat");
            FontRenderers.sf_bold = FontRenderers.create(16f, "sf_bold");
            FontRenderers.sf_bold18 = FontRenderers.create(18f, "sf_bold");
            FontRenderers.sf_bold20 = FontRenderers.create(20f, "sf_bold");
            FontRenderers.sf_bold62 = FontRenderers.create(62f, "sf_bold");
            FontRenderers.misans20 = FontRenderers.create(20f, "misans");
            FontRenderers.misans18 = FontRenderers.create(18f, "misans");
            FontRenderers.misans14 = FontRenderers.create(14f, "misans");
            FontRenderers.misans16 = FontRenderers.create(16f, "misans");
            FontRenderers.sf_medium = FontRenderers.create(16f, "sf_medium");
            FontRenderers.sf_medium_mini = FontRenderers.create(12f, "sf_medium");
            FontRenderers.sf_medium_modules = FontRenderers.create(14f, "sf_medium");
            FontRenderers.sf_bold_mini = FontRenderers.create(14f, "sf_bold");
            FontRenderers.sf_bold_micro = FontRenderers.create(12f, "sf_bold");
            FontRenderers.profont = FontRenderers.create(16f, "profont");
            FontRenderers.icons = FontRenderers.create(24, "icons");
            FontRenderers.icons2_30 = FontRenderers.create(30, "icons2");
            FontRenderers.mid_icons = FontRenderers.create(34, "icons");
            FontRenderers.big_icons = FontRenderers.create(72, "icons");
        } catch (Exception e) {
            ExternalUI.log(e.getMessage());
        }
    }
}