package com.leave.ink.features.module.modules.world;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventPacket;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.events.EventWorld;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.client.ChatUtils;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.ClientboundAddPlayerPacket;
import net.minecraft.network.protocol.game.ClientboundRemoveEntitiesPacket;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;


import java.util.*;

@ModuleInfo(name = {
        @Text(label = "AntiBot", language = Language.English),
        @Text(label = "防机器人", language = Language.Chinese)
}, category = Category.World)
public class AntiBot extends Module {
    @SettingInfo(name = {
            @Text(label = "Tick", language = Language.English),
            @Text(label = "存活", language = Language.Chinese)
    })
    private final BooleanSetting ticksExisted = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "TicksExisted", language = Language.English),
            @Text(label = "存活时刻", language = Language.Chinese)
    })
    private final NumberSetting TicksExisted = new NumberSetting(10, 200, 1, "#");
    @SettingInfo(name = {
            @Text(label = "Unfair", language = Language.English),
            @Text(label = "不正确的", language = Language.Chinese)
    })
    private final BooleanSetting unfairName = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Matrix", language = Language.English),
            @Text(label = "矩阵", language = Language.Chinese)
    })
    private static final BooleanSetting matrix = new BooleanSetting(false);

    public AntiBot() {
        registerSetting(ticksExisted, TicksExisted, unfairName, matrix);
    }

    private static final List<Entity> entityList = new ArrayList<>();
    private final Map<Integer, String> uuidDisplayNames = new HashMap<>();
    private final Map<Integer, String> entityIdDisplayNames = new HashMap<>();
    private final Map<Integer, UUID> uuids = new HashMap<>();
    private static final Set<Integer> ids = new HashSet<>();

    @Override
    protected void onDisable() {
        if (Utils.isNull())
            return;

        entityList.clear();
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        for (Entity entity : mc.level.entitiesForRendering()) {
            if (entity instanceof Player player && player != mc.player) {
                if (ticksExisted.getValue() && player.tickCount > TicksExisted.getValue().intValue()) {
                    if (!entityList.contains(player))
                        entityList.add(player);
                }

                if (unfairName.getValue() && player.getName().getString().length() >= 15) {
                    if (!entityList.contains(player))
                        entityList.add(player);
                }

                if (mc.getConnection().getPlayerInfo(player.getUUID()) == null)
                    if (!entityList.contains(player))
                        entityList.add(player);
            }
        }
    }

    @EventTarget
    public void onPacket(EventPacket event) {
        if (event.getPacketType() == EventPacket.PacketType.Server) {
            if (event.getPacket() instanceof ClientboundAddPlayerPacket packet) {
                if (uuids.containsKey(packet.getPlayerId())) {
                    String displayName = uuidDisplayNames.get(packet.getPlayerId());
                    ChatUtils.displayAlert("Bot Detected! (" + displayName + ")");
                    entityIdDisplayNames.put(packet.getEntityId(), displayName);
                    uuids.remove(packet.getPlayerId());
                    ids.add(packet.getEntityId());
                }
            } else if (event.getPacket() instanceof ClientboundRemoveEntitiesPacket packet) {
                for (Integer entityId : packet.getEntityIds()) {
                    if (ids.contains(entityId)) {
                        String displayName = entityIdDisplayNames.get(entityId);
                        ChatUtils.displayAlert("Bot Removed! (" + displayName + ")");
                        ids.remove(entityId);
                    }
                }
            }
        }
    }

    @EventTarget
    public void onWorld(EventWorld event) {
        if (Utils.isNull())
            return;

        entityList.clear();
    }

    public static boolean isBot(LivingEntity entity) {
        if (entity instanceof Player player) {
            return Main.INSTANCE.moduleManager.getModule("AntiBot").isEnable() && entityList.contains(player);
        }

        if (matrix.getValue()) {
            return ids.contains(entity.getId());
        }

        return false;
    }
}
