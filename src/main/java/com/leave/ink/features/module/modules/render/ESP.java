package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventRender2D;
import com.leave.ink.events.EventRender3D;
import com.leave.ink.events.EventRenderLiving;
import com.leave.ink.events.EventType;
import com.leave.ink.features.hud.main.AuraSyncHud;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ColorSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.manager.HeypixelManager;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;
import com.leave.ink.utils.render.Drawing;
import com.leave.ink.utils.render.RenderUtils;
import com.leave.ink.ui.skija.utils.Skia3DRenderer;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.ui.skija.font.IconFont;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.*;

import net.minecraft.client.Camera;
import net.minecraft.client.gui.Font;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.model.CreeperModel;
import net.minecraft.client.model.EntityModel;
import net.minecraft.client.model.HumanoidModel;

import net.minecraft.client.model.SpiderModel;
import net.minecraft.client.model.geom.ModelPart;

import net.minecraft.util.Mth;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.decoration.ArmorStand;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;


import org.joml.*;

import java.awt.*;
import java.lang.Math;
import java.text.DecimalFormat;
import java.util.*;
import java.util.List;
import java.util.regex.Pattern;

import static com.leave.ink.features.module.modules.render.esp.ESPUtils.*;

@ModuleInfo(name = {
        @Text(label = "ESP", language = Language.English),
        @Text(label = "实体边框", language = Language.Chinese)
}, category = Category.Render)
public class ESP extends Module {
    @SettingInfo(name = {
            @Text(label = "HealthBarMode", language = Language.English),
            @Text(label = "生命条样式", language = Language.Chinese)
    })
    public final ModeSetting healthMode = new ModeSetting("Line", Arrays.asList("Line", "Dot"));
    @SettingInfo(name = {
            @Text(label = "HealthString", language = Language.English),
            @Text(label = "显示生命值", language = Language.Chinese)
    })
    public final BooleanSetting healthString = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Health-Bar", language = Language.English),
            @Text(label = "显示生命条", language = Language.Chinese)
    })
    public final BooleanSetting showHealth = new BooleanSetting(true,
            new SettingAttribute<>(healthMode, true),
            new SettingAttribute<>(healthString, true)
    );

    @SettingInfo(name = {
            @Text(label = "Outline", language = Language.English),
            @Text(label = "描边", language = Language.Chinese)
    })
    public final BooleanSetting outline = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Box", language = Language.English),
            @Text(label = "方框", language = Language.Chinese)
    })
    public final BooleanSetting box = new BooleanSetting(true,
            new SettingAttribute<>(outline, true)
    );
    @SettingInfo(name = {
            @Text(label = "Self", language = Language.English),
            @Text(label = "绘制自己", language = Language.Chinese)
    })
    public final BooleanSetting self = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Armor", language = Language.English),
            @Text(label = "装备", language = Language.Chinese)
    })
    public final BooleanSetting armor = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Tags", language = Language.English),
            @Text(label = "标签", language = Language.Chinese)
    })
    public final BooleanSetting tags = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Mode", language = Language.English),
            @Text(label = "模式", language = Language.Chinese)
    })
    public final ModeSetting modeValue = new ModeSetting("2D", Arrays.asList("2D", "Glow"),
            new SettingAttribute<>(showHealth, "2D"),
            new SettingAttribute<>(box, "2D"),
            new SettingAttribute<>(self, "2D"),
            new SettingAttribute<>(armor, "2D"),
            new SettingAttribute<>(tags, "2D")
    );

    @SettingInfo(name = {
            @Text(label = "Render", language = Language.English),
            @Text(label = "渲染", language = Language.Chinese)
    })
    public final ModeSetting renderEngine = new ModeSetting("MC", Arrays.asList("MC", "Skia"));
    @SettingInfo(name = {
            @Text(label = "LineMode", language = Language.English),
            @Text(label = "线条模式", language = Language.Chinese)
    })
    public final ModeSetting lineMode = new ModeSetting("None", Arrays.asList("Skeleton", "Wireframe", "None"));


    @SettingInfo(name = {
            @Text(label = "Color", language = Language.English),
            @Text(label = "颜色", language = Language.Chinese)
    })
    private static final ColorSetting colorValue = new ColorSetting(new Color(255, 255, 255, 255));
    @SettingInfo(name = {
            @Text(label = "AuraSync", language = Language.English),
            @Text(label = "灯光同步", language = Language.Chinese)
    })
    public static final BooleanSetting auraSync = new BooleanSetting(false,
            new SettingAttribute<>(colorValue, false)
    );
    public ESP() {
        registerSetting(modeValue, lineMode, renderEngine, auraSync);
    }
    private final Map<Integer, ESPU> map = new HashMap<>();

    private final DecimalFormat dFormat = new DecimalFormat("0.0");

    private int index = 0;
    public static int getESPColor() {
        return ESP.auraSync.getValue() ? AuraSyncHud.getColor(0,0) : colorValue.getValue().getRGB();

    }
    @EventTarget
    public void onRenderLiving(EventRenderLiving evt) {
        if(evt.getEventType() != EventType.MIDDLE) return;
        if(lineMode.getValue().equals("None"))
            return;

        LivingEntity livingEntity = evt.getEntity();
        if (!Utils.isValidEntity(livingEntity)) {
            return;
        }
        if(livingEntity instanceof ArmorStand) return;

        EntityModel<?> entityModel = evt.getRenderer().getModel();

        PoseStack poseStack = evt.getPoseStack();
        int auraColor = auraSync.getValue() ? AuraSyncHud.getColor(0,0) : colorValue.getValue().getRGB();
        List<Bone> bones = new ArrayList<>();
        Map<ModelPart, BoneData> boneDataMap = new HashMap<>();

        if(entityModel instanceof HumanoidModel<?> playerModel) {

            List<ModelPart> parts = Arrays.asList(
                    playerModel.head,
                    playerModel.body,
                    playerModel.leftArm,
                    playerModel.rightArm,
                    playerModel.leftLeg,
                    playerModel.rightLeg
            );
            for(ModelPart part : parts) {

                List<ModelPart.Cube> cubes = ObfuscationReflectionHelper.getPrivateValue(ModelPart.class, part, "cubes");
                if(cubes == null) continue;
                ModelPart.Cube cube = cubes.get(0);
                boneDataMap.put(part, translateBone(part, cube));
            }
            BoneData head = boneDataMap.get(playerModel.head);
            BoneData body = boneDataMap.get(playerModel.body);
            BoneData lArm = boneDataMap.get(playerModel.leftArm);
            BoneData rArm = boneDataMap.get(playerModel.rightArm);
            BoneData lLeg = boneDataMap.get(playerModel.leftLeg);
            BoneData rLeg = boneDataMap.get(playerModel.rightLeg);
            if(lineMode.getValue().equals("Skeleton")) {
                bones.add(new Bone(head.center(), body.topCenter()));
                bones.add(new Bone(body.topCenter(), body.bottomCenter()));
                bones.add(new Bone(body.topCenter(), lArm.topCenter()));
                bones.add(new Bone(lArm.topCenter(), lArm.bottomCenter()));
                bones.add(new Bone(body.topCenter(), rArm.topCenter()));
                bones.add(new Bone(rArm.topCenter(), rArm.bottomCenter()));
                bones.add(new Bone(body.bottomCenter(), lLeg.topCenter()));
                bones.add(new Bone(lLeg.topCenter(), lLeg.bottomCenter()));
                bones.add(new Bone(body.bottomCenter(), rLeg.topCenter()));
                bones.add(new Bone(rLeg.topCenter(), rLeg.bottomCenter()));

            }
            else {

                for (ModelPart part : boneDataMap.keySet()) {
                    BoneData bd = boneDataMap.get(part);
                    bones.add(new Bone(bd.v0(), bd.v1()));
                    bones.add(new Bone(bd.v1(), bd.v2()));
                    bones.add(new Bone(bd.v2(), bd.v3()));
                    bones.add(new Bone(bd.v3(), bd.v0()));
                    bones.add(new Bone(bd.v4(), bd.v5()));
                    bones.add(new Bone(bd.v5(), bd.v6()));
                    bones.add(new Bone(bd.v6(), bd.v7()));
                    bones.add(new Bone(bd.v7(), bd.v4()));
                    bones.add(new Bone(bd.v0(), bd.v4()));
                    bones.add(new Bone(bd.v1(), bd.v5()));
                    bones.add(new Bone(bd.v2(), bd.v6()));
                    bones.add(new Bone(bd.v3(), bd.v7()));
                }
            }
        }
        if(entityModel instanceof SpiderModel<?> spiderModel) {
            ModelPart root = ObfuscationReflectionHelper.getPrivateValue(SpiderModel.class, spiderModel, "root");
            List<ModelPart> parts = new ArrayList<>(root.getAllParts().toList());

            for(ModelPart part : parts) {
                List<ModelPart.Cube> cubes = ObfuscationReflectionHelper.getPrivateValue(ModelPart.class, part, "cubes");
                if(cubes == null) continue;
                if(cubes.size() == 0)
                    continue;
                ModelPart.Cube cube = cubes.get(0);
                boneDataMap.put(part, translateBone(part, cube));
            }
            //12
            BoneData a = boneDataMap.get(parts.get(1));//head
            BoneData b = boneDataMap.get(parts.get(2));
            BoneData c = boneDataMap.get(parts.get(3));
            BoneData d = boneDataMap.get(parts.get(4));
            BoneData e = boneDataMap.get(parts.get(7));
            BoneData f = boneDataMap.get(parts.get(8));
            BoneData g = boneDataMap.get(parts.get(9));
            BoneData h = boneDataMap.get(parts.get(10));
            BoneData i = boneDataMap.get(parts.get(11));

            BoneData k = boneDataMap.get(parts.get(5));
            BoneData l = boneDataMap.get(parts.get(6));

            if(lineMode.getValue().equals("Skeleton")) {
                bones.add(new Bone(a.center(), a.backCenter()));
                bones.add(new Bone(k.frontCenter(), k.backCenter()));
                bones.add(new Bone(l.frontCenter(), l.backCenter()));

                bones.add(new Bone(k.frontCenter(), l.backCenter()));

                bones.add(new Bone(b.leftCenter(), b.rightCenter()));
                bones.add(new Bone(c.leftCenter(), c.rightCenter()));
                bones.add(new Bone(d.leftCenter(), d.rightCenter()));
                bones.add(new Bone(e.leftCenter(), e.rightCenter()));
                bones.add(new Bone(f.leftCenter(), f.rightCenter()));
                bones.add(new Bone(g.leftCenter(), g.rightCenter()));
                bones.add(new Bone(h.leftCenter(), h.rightCenter()));
                bones.add(new Bone(i.leftCenter(), i.rightCenter()));
            }
        }
        if(entityModel instanceof CreeperModel<?> creeperModel) {
            ModelPart root = ObfuscationReflectionHelper.getPrivateValue(CreeperModel.class, creeperModel, "root");
            List<ModelPart> parts = new ArrayList<>(root.getAllParts().toList());

            for(ModelPart part : parts) {
                List<ModelPart.Cube> cubes = ObfuscationReflectionHelper.getPrivateValue(ModelPart.class, part, "cubes");
                if(cubes == null) continue;
                if(cubes.size() == 0)
                    continue;
                ModelPart.Cube cube = cubes.get(0);
                boneDataMap.put(part, translateBone(part, cube));
            }

        }


        if(lineMode.getValue().equals("Wireframe")) {
            for (ModelPart part : boneDataMap.keySet()) {
                BoneData bd = boneDataMap.get(part);
                bones.add(new Bone(bd.v0(), bd.v1()));
                bones.add(new Bone(bd.v1(), bd.v2()));
                bones.add(new Bone(bd.v2(), bd.v3()));
                bones.add(new Bone(bd.v3(), bd.v0()));
                bones.add(new Bone(bd.v4(), bd.v5()));
                bones.add(new Bone(bd.v5(), bd.v6()));
                bones.add(new Bone(bd.v6(), bd.v7()));
                bones.add(new Bone(bd.v7(), bd.v4()));
                bones.add(new Bone(bd.v0(), bd.v4()));
                bones.add(new Bone(bd.v1(), bd.v5()));
                bones.add(new Bone(bd.v2(), bd.v6()));
                bones.add(new Bone(bd.v3(), bd.v7()));
            }
        }
        renderSkeleton(poseStack, evt.getMultiBufferSource(), 0, 0, bones, auraColor);

        index++;
        if(index > 1000) index = 0;
    }


    @EventTarget
    public void onDrawing2d(EventRender2D event) {
        if(!modeValue.getValue().equals("2D")) return;

        if (renderEngine.getValue().equals("Skia")) {
            renderWithSkia(event);
        } else {
            renderWithMC(event);
        }
    }

    private void renderWithSkia(EventRender2D event) {
        CanvasStack canvasStack = Skia3DRenderer.getSkiaCanvas();
        if (canvasStack == null) {
            renderWithMC(event);
            return;
        }

        int index = 0;
        for (Map.Entry<Integer, ESPU> entry : map.entrySet()) {
            if (!entry.getValue().shouldDraw) continue;

            boolean draw = false;
            LivingEntity livingEntity = null;
            assert mc.level != null;
            for (Entity entity : mc.level.entitiesForRendering()) {
                if (entity instanceof LivingEntity) {
                    if(entity == mc.player) {
                        if(mc.options.getCameraType().isFirstPerson()) continue;
                        if(!self.getValue()) continue;
                    }
                    if (entity.getId() == entry.getKey()) {
                        if (!Utils.isValidEntity(((LivingEntity) entity))) {
                            map.remove(entry.getKey());
                            continue;
                        }
                        livingEntity = (LivingEntity) entity;
                        draw = true;
                        break;
                    }
                }
            }

            ++index;
            if (draw && livingEntity != null) {
                ESPU espu = entry.getValue();
                renderSkiaESP(canvasStack, espu, livingEntity, index);
            }
        }
    }

    private void renderWithMC(EventRender2D event) {
        PoseStack poseStack = event.getPoseStack();
        GuiGraphics guiGraphics = event.getGuiGraphics();
        int index = 0;
        for (Map.Entry<Integer, ESPU> entry : map.entrySet()) {
            if (!entry.getValue().shouldDraw) continue;
            boolean draw = false;
            assert mc.level != null;
            for (Entity entity : mc.level.entitiesForRendering()) {
                if (entity instanceof LivingEntity) {
                    if(entity == mc.player )
                    {
                        if(mc.options.getCameraType().isFirstPerson()) continue;
                        if(!self.getValue()) continue;
                    }
                    if (entity.getId() == entry.getKey()) {
                        if (!Utils.isValidEntity(((LivingEntity) entity))) {
                            map.remove(entry.getKey());
                            continue;
                        }
                        draw = true;
                        break;
                    }
                }
            }

            ++index;
            if (draw) {
                ESPU espu = entry.getValue();
                LivingEntity livingEntity = espu.entity;
                float posX = espu.leftPoint;
                float posY = espu.topPoint;
                float endPosX = espu.rightPoint;
                float endPosY = espu.bottomPoint;
                Drawing drawing = Drawing.startDrawRect(poseStack);
                if(box.getValue()) {
                    if(outline.getValue()) {
                        Drawing.drawing(drawing,posX - 1.0D, posY, posX + 0.5D, endPosY + 0.5D, Color.BLACK.getRGB());
                        Drawing.drawing(drawing,posX - 1.0D, posY - 0.5D, endPosX + 0.5D, posY + 0.5D + 0.5D, Color.BLACK.getRGB());
                        Drawing.drawing(drawing,endPosX - 0.5D - 0.5D, posY, endPosX + 0.5D, endPosY + 0.5D, Color.BLACK.getRGB());
                        Drawing.drawing(drawing,posX - 1.0D, endPosY - 0.5D - 0.5D, endPosX + 0.5D, endPosY + 0.5D, Color.BLACK.getRGB());
                    }

                    int auraColor = auraSync.getValue() ? AuraSyncHud.getColor(index,index * 10) : colorValue.getValue().getRGB();
                    Drawing.drawing(drawing,posX - 0.5D, posY, posX + 0.5D - 0.5D, endPosY, auraColor);
                    Drawing.drawing(drawing,posX, endPosY - 0.5D, endPosX, endPosY, auraColor);
                    Drawing.drawing(drawing,posX - 0.5D, posY, endPosX, posY + 0.5D, auraColor);
                    Drawing.drawing(drawing,endPosX - 0.5D, posY, endPosX, endPosY, auraColor);
                }

                Color backgroundColor = new Color(0, 0, 0, 120);
                double fontScale = 0.5d;
                if(showHealth.getValue()) {
                    float entityHealth = HeypixelManager.getEntityHealth(livingEntity);
                    double healthRate = HeypixelManager.getHealthRate(livingEntity);
                    double textWidth = (endPosY - posY) * healthRate;

                    String healthDisplay = dFormat.format(entityHealth) + " §c❤";
                    String healthPercent = ((int) (healthRate * 100F)) + "%";

//                    if (healthNumber.isEnable() && (!hoverValue.isEnable() || entity == mc.player || isHovering(posX, endPosX, posY, endPosY, scaledResolution)))
                    if(healthString.getValue()) {
                        Drawing.stopDrawingRect(drawing.getTessellator());
                        drawScaledString(guiGraphics,healthDisplay, posX - 4.0 - mc.font.width(healthDisplay) * fontScale, (endPosY - textWidth) - mc.font.lineHeight / 2F * fontScale, fontScale, -1);
                        drawing = Drawing.startDrawRect(poseStack);

                    }

                    Drawing.drawing(drawing,posX - 3.5D, posY - 0.5D, posX - 1.5D, endPosY + 0.5D, backgroundColor.getRGB());
                    if (entityHealth > 0.0F) {
                        int healthColor = RenderUtils.healthColor(entityHealth, livingEntity.getMaxHealth(), 255).getRGB();
                        double deltaY = endPosY - posY;
                        if(healthMode.getValue().equals("Dot") && deltaY >= 60) {
                            for (double k = 0; k < 10; k++) {
                                double reratio = Mth.clamp(entityHealth - k * (livingEntity.getMaxHealth() / 10D), 0D, livingEntity.getMaxHealth() / 10D) / (livingEntity.getMaxHealth() / 10D);
                                double hei = (deltaY / 10D - 0.5) * reratio;
                                Drawing.drawing(drawing,posX - 3.0D, endPosY - (deltaY + 0.5) / 10D * k - hei, posX - 2.0D, endPosY - (deltaY + 0.5) / 10D * k, healthColor);
                            }
                        }else {
                            Drawing.drawing(drawing, posX - 3.0D, endPosY - textWidth, posX - 2.0D, endPosY, healthColor);       Drawing.drawing(drawing, posX - 3.0D, endPosY - textWidth, posX - 2.0D, endPosY, healthColor);
                        }

                    }
                }

                if(livingEntity instanceof Player && armor.getValue()) {
                    final double constHeight = (endPosY - posY) / 4.0;
                    int n = 0;
                    for (int m = 4; m > 0; m--) {
                        ItemStack armorStack = ((Player) livingEntity).inventoryMenu.getSlot(9-m).getItem();
                        if(armorStack.isEmpty()) n++;
                    }
                    if(n != 4) {
                        for (int m = 4; m > 0; m--) {
                            ItemStack armorStack = ((Player) livingEntity).inventoryMenu.getSlot(9-m).getItem();
                            double theHeight = constHeight + 0.25D;
                            Drawing.drawing(drawing, endPosX + 1.5D, endPosY + 0.5D - theHeight * m, endPosX + 3.5D, endPosY + 0.5D - theHeight * (m - 1), backgroundColor.getRGB());
                            Drawing.drawing(drawing,endPosX + 2.0D,
                                    endPosY + 0.5D - theHeight * (m - 1) - 0.25D - (constHeight - 0.25D) * Mth.clamp((double) getItemDurability(armorStack) / (double) armorStack.getMaxDamage(), 0D, 1D),
                                    endPosX + 3.0D,
                                    endPosY + 0.5D - theHeight * (m - 1) - 0.25D, new Color(0, 255, 255).getRGB());
                        }
                    }

                }
                String entName = livingEntity.getName().getString();
//                if (tagsBGValue.isEnable())
//                    Drawing.drawing(drawing, posX + (endPosX - posX) / 2F - (mc.font.width(entName) / 2F + 2F) * fontScale, posY - 1F - (mc.font.lineHeight + 2F) * fontScale+ endPosY - posY, posX + (endPosX - posX) / 2F + (mc.font.width(entName) / 2F + 2F) * fontScale, posY - 1F + 2F * fontScale + endPosY - posY, new Color(24,24,24,111).getRGB());
                Drawing.stopDrawingRect(drawing.getTessellator());
                if(tags.getValue())
                    drawScaledCenteredString(guiGraphics, entName, posX + (endPosX - posX) / 2F, posY - 1F - mc.font.lineHeight * fontScale+ (endPosY - posY) + mc.font.lineHeight * fontScale + 2, fontScale, -1);
            }
        }
    }
    private void renderSkiaESP(CanvasStack canvasStack, ESPU espu, LivingEntity livingEntity, int index) {
        canvasStack.push();

        float posX = espu.leftPoint;
        float posY = espu.topPoint;
        float endPosX = espu.rightPoint;
        float endPosY = espu.bottomPoint;
        if (box.getValue()) {
            int auraColor = auraSync.getValue() ? AuraSyncHud.getColor(index, index * 10) : colorValue.getValue().getRGB();

            if (outline.getValue()) {
                float outlineWidth = 1.0f;
                float x1 = posX - 1.0f;
                float y1 = posY - 0.5f;
                float x2 = endPosX + 0.5f;
                float y2 = endPosY + 0.5f;

                SkiaRender.drawLine(canvasStack, x1, y1, x2, y1, outlineWidth, Color.BLACK.getRGB());
                SkiaRender.drawLine(canvasStack, x1, y2, x2, y2, outlineWidth, Color.BLACK.getRGB());
                SkiaRender.drawLine(canvasStack, x1, y1, x1, y2, outlineWidth, Color.BLACK.getRGB());
                SkiaRender.drawLine(canvasStack, x2, y1, x2, y2, outlineWidth, Color.BLACK.getRGB());
            }

            float lineWidth = 1.0f;
            SkiaRender.drawRect(canvasStack, posX - 0.5f, posY, lineWidth, endPosY - posY, auraColor);
            SkiaRender.drawRect(canvasStack, endPosX - 0.5f, posY, lineWidth, endPosY - posY, auraColor);
            SkiaRender.drawRect(canvasStack, posX - 0.5f, posY, endPosX - posX + 1.0f, lineWidth, auraColor);
            SkiaRender.drawRect(canvasStack, posX - 0.5f, endPosY - 0.5f, endPosX - posX + 1.0f, lineWidth, auraColor);
        }

        if (showHealth.getValue()) {
            renderSkiaHealthBar(canvasStack, espu, livingEntity, posX, posY, endPosX, endPosY);
        }

        if (livingEntity instanceof Player && armor.getValue()) {
            renderSkiaArmor(canvasStack, (Player) livingEntity, posX, posY, endPosX, endPosY);
        }

        if (tags.getValue()) {
            renderSkiaNameTag(canvasStack, livingEntity, posX, posY, endPosX, endPosY);
        }

        canvasStack.pop();
    }

    private void renderSkiaHealthBar(CanvasStack canvasStack, ESPU espu, LivingEntity livingEntity,
                                   float posX, float posY, float endPosX, float endPosY) {
        float entityHealth = HeypixelManager.getEntityHealth(livingEntity);
        double healthRate = HeypixelManager.getHealthRate(livingEntity);
        double textWidth = (endPosY - posY) * healthRate;

        Color backgroundColor = new Color(0, 0, 0, 120);
        SkiaRender.drawRect(canvasStack, posX - 3.5f, posY - 0.5f, 2.0f, endPosY - posY + 1.0f, backgroundColor.getRGB());

        if (entityHealth > 0.0F) {
            int healthColor = RenderUtils.healthColor(entityHealth, livingEntity.getMaxHealth(), 255).getRGB();
            double deltaY = endPosY - posY;

            if (healthMode.getValue().equals("Dot") && deltaY >= 60) {
                for (double k = 0; k < 10; k++) {
                    double reratio = Mth.clamp(entityHealth - k * (livingEntity.getMaxHealth() / 10D), 0D, livingEntity.getMaxHealth() / 10D) / (livingEntity.getMaxHealth() / 10D);
                    double hei = (deltaY / 10D - 0.5) * reratio;
                    SkiaRender.drawRect(canvasStack, posX - 3.0f,
                            (float)(endPosY - (deltaY + 0.5) / 10D * k - hei),
                            1.0f, (float)hei, healthColor);
                }
            } else {
                SkiaRender.drawRect(canvasStack, posX - 3.0f, (float)(endPosY - textWidth),
                        1.0f, (float)textWidth, healthColor);
            }
        }
        if (healthString.getValue()) {
            String healthText = dFormat.format(entityHealth);
            double fontScale = 0.5d;
            float healthTextWidth = SkiaFontManager.getDefaultFont24().getWidth(healthText) * (float)fontScale;
            float iconSize = 40f * (float)fontScale;
            float spacing = 1f;
            float totalWidth = healthTextWidth + iconSize + spacing;
            float textX = posX + 8f - totalWidth;
            float textY = (float)((endPosY - textWidth) - mc.font.lineHeight / 2F * fontScale);
            canvasStack.push();
            canvasStack.canvas.scale((float)fontScale, (float)fontScale);
            SkiaFontManager.getDefaultFont24().drawText(canvasStack, healthText,
                    textX / (float)fontScale, textY / (float)fontScale, Color.WHITE.getRGB());
            canvasStack.pop();
            float iconY = textY - (iconSize * 0.07f);
            renderHealthIcon(canvasStack, textX + healthTextWidth + spacing, iconY, iconSize, Color.RED.getRGB());
        }
    }

    private void renderSkiaArmor(CanvasStack canvasStack, Player player, float posX, float posY, float endPosX, float endPosY) {
        final double constHeight = (endPosY - posY) / 4.0;
        int n = 0;
        for (int m = 4; m > 0; m--) {
            ItemStack armorStack = player.inventoryMenu.getSlot(9-m).getItem();
            if(armorStack.isEmpty()) n++;
        }

        if(n != 4) {
            Color backgroundColor = new Color(0, 0, 0, 120);
            for (int m = 4; m > 0; m--) {
                ItemStack armorStack = player.inventoryMenu.getSlot(9-m).getItem();
                double theHeight = constHeight + 0.25D;

                SkiaRender.drawRect(canvasStack, endPosX + 1.5f,
                        (float)(endPosY + 0.5D - theHeight * m),
                        2.0f, (float)theHeight, backgroundColor.getRGB());
                if (!armorStack.isEmpty()) {
                    double durabilityRate = Mth.clamp((double) getItemDurability(armorStack) / (double) armorStack.getMaxDamage(), 0D, 1D);
                    float barHeight = (float)((constHeight - 0.25D) * durabilityRate);
                    SkiaRender.drawRect(canvasStack, endPosX + 2.0f,
                            (float)(endPosY + 0.5D - theHeight * (m - 1) - 0.25D - barHeight),
                            1.0f, barHeight, new Color(0, 255, 255).getRGB());
                }
            }
        }
    }

    private void renderSkiaNameTag(CanvasStack canvasStack, LivingEntity livingEntity, float posX, float posY, float endPosX, float endPosY) {
        String entName = livingEntity.getName().getString();
        double fontScale = 0.5d;

        float centerX = posX + (endPosX - posX) / 2F;
        float textY = posY - 1F - mc.font.lineHeight * (float)fontScale + (endPosY - posY) + mc.font.lineHeight * (float)fontScale + 2;
        float textX = centerX - mc.font.width(entName) / 2F * (float)fontScale;

        canvasStack.push();
        canvasStack.canvas.scale((float)fontScale, (float)fontScale);
        SkiaFontManager.getDefaultFont12().drawText(canvasStack, entName,
                textX / (float)fontScale, textY / (float)fontScale, Color.WHITE.getRGB());
        canvasStack.pop();
    }

    private void renderHealthIcon(CanvasStack canvasStack, float x, float y, float size, int color) {
        try {
            String heartIconChar = "P";
            SkiaRender.drawIconsFont(canvasStack, heartIconChar, x, y, size, color);
        } catch (Exception e1) {
            try {
                String heartSymbol = "♥";
                String mappedChar = IconFont.getIconChar(heartSymbol);
                SkiaFontManager.getIconFont((int)size).drawText(canvasStack, mappedChar, x, y, color);
            } catch (Exception e2) {
                renderGeometricHeart(canvasStack, x, y, size, color);
            }
        }
    }

    private void renderGeometricHeart(CanvasStack canvasStack, float x, float y, float size, int color) {
        canvasStack.push();
        float halfSize = size * 0.6f;
        float quarterSize = size * 0.3f;
        SkiaRender.drawCircle(canvasStack, x + quarterSize, y + quarterSize, quarterSize, color);
        SkiaRender.drawCircle(canvasStack, x + halfSize + quarterSize/2, y + quarterSize, quarterSize, color);
        SkiaRender.drawRect(canvasStack, x + quarterSize/2, y + quarterSize,
                halfSize + quarterSize/2, quarterSize, color);
        float triangleWidth = halfSize;
        for (int i = 0; i < 4; i++) {
            float currentY = y + halfSize + i * 1.5f;
            float currentWidth = triangleWidth - i * (triangleWidth / 4f);
            float offsetX = (triangleWidth - currentWidth) / 2f;

            SkiaRender.drawRect(canvasStack, x + quarterSize + offsetX, currentY,
                    currentWidth, 1.5f, color);
        }

        canvasStack.pop();
    }



    private void drawScaledCenteredString(GuiGraphics guiGraphics,  String text, double x, double y, double scale, int color) {
        drawScaledString(guiGraphics, text, x - mc.font.width(text) / 2F * scale, y, scale, color);
    }
    private final Pattern COLOR_PATTERN = Pattern.compile("(?i)§[0-9A-FK-OR]");
    public void drawOutlineString(GuiGraphics guiGraphics, String s, float x, float y, int color, Font fontRenderer) {
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableDepthTest();
        guiGraphics.drawString(fontRenderer, stripColor(s), (int) (x * 2 - 1), (int) (y * 2), Color.BLACK.getRGB());
        guiGraphics.drawString(fontRenderer, stripColor(s), (int) (x * 2 + 1), (int) (y * 2), Color.BLACK.getRGB());
        guiGraphics.drawString(fontRenderer, stripColor(s),(int) (x * 2), (int) (y * 2 - 1), Color.BLACK.getRGB());
        guiGraphics.drawString(fontRenderer, stripColor(s), (int) (x * 2), (int) (y * 2 + 1), Color.BLACK.getRGB());
        guiGraphics.drawString(fontRenderer, s, (int) (x * 2), (int) (y * 2), color);
        RenderSystem.enableDepthTest();
        RenderSystem.disableBlend();
    }
    private String stripColor(String input) {
        return COLOR_PATTERN.matcher(input).replaceAll("");
    }
    private void drawScaledString(GuiGraphics guiGraphics,String text, double x, double y, double scale, int color) {
        PoseStack poseStack = guiGraphics.pose();
        poseStack.pushPose();
        poseStack.translate(x,y,0);
        poseStack.scale((float) scale, (float) scale, (float) scale);


        drawOutlineString(guiGraphics, text, 0, 0, color, mc.font);
        poseStack.popPose();

    }
    private int getItemDurability(ItemStack stack) {
        if (stack == null) return 0;
        else return stack.getMaxDamage() - stack.getDamageValue();
    }
    @EventTarget
    public void onEventRender3D(EventRender3D event) {
        Camera camera = mc.gameRenderer.getMainCamera();
        assert mc.level != null;
        for (Entity entity : mc.level.entitiesForRendering()) {
            if (entity instanceof LivingEntity livingEntity) {

                if(entity == mc.player )
                {
                    if(mc.options.getCameraType().isFirstPerson()) continue;
                    if(!self.getValue()) continue;
                }

                if (!Utils.isValidEntity(((LivingEntity) entity))) {
                    continue;
                }
                float partialTicks = mc.getFrameTime();
                double x = Mth.lerp(partialTicks, entity.xOld, entity.getX());
                double y = Mth.lerp(partialTicks, entity.yOld, entity.getY());
                double z = Mth.lerp(partialTicks, entity.zOld, entity.getZ());
                double width = (double) entity.getBbWidth() / 2.0D; // 改为除以2，更接近实际大小
                double height = (double) entity.getBbHeight() + (entity.isShiftKeyDown() ? -0.2D : 0.1D); // 微调高度偏移

                AABB aabb = new AABB(x - width, y, z - width, x + width, y + height, z + width);
                List<Vec3> vectors = Arrays.asList(new Vec3(aabb.minX, aabb.minY, aabb.minZ), new Vec3(aabb.minX, aabb.maxY, aabb.minZ), new Vec3(aabb.maxX, aabb.minY, aabb.minZ), new Vec3(aabb.maxX, aabb.maxY, aabb.minZ), new Vec3(aabb.minX, aabb.minY, aabb.maxZ), new Vec3(aabb.minX, aabb.maxY, aabb.maxZ), new Vec3(aabb.maxX, aabb.minY, aabb.maxZ), new Vec3(aabb.maxX, aabb.maxY, aabb.maxZ));
                Vector4d position = null;

                for (Vec3 vector : vectors) {
                    Vec3 cameraPos = camera.getPosition();
                    Vec3 vec3 = new Vec3(
                        vector.x - cameraPos.x,
                        vector.y - cameraPos.y,
                        vector.z - cameraPos.z
                    );

                    Vec3 projected = Main.INSTANCE.projection.projectToScreen(vec3);

                    if(projected != null && projected.z >= 0.0D && projected.z < 1.0D) {
                        if (position == null) {
                            position = new Vector4d(projected.x, projected.y, projected.z, 0.0D);
                        }
                        position.x = Math.min(projected.x, position.x);
                        position.y = Math.min(projected.y, position.y);
                        position.z = Math.max(projected.x, position.z);
                        position.w = Math.max(projected.y, position.w);
                    }
                }
                boolean shouldRender = position != null;
                if(map.get(entity.getId()) != null) {
                    map.get(entity.getId()).shouldDraw = shouldRender;
                }
                if (shouldRender) {
                    float margin = 1.0f;
                    float posX = (float) position.x - margin;
                    float posY = (float) position.y - margin;
                    float endPosX = (float) position.z + margin;
                    float endPosY = (float) position.w + margin;
                    if(map.get(entity.getId()) == null)
                        map.put(entity.getId(), new ESPU(livingEntity, posX, posY, endPosX, endPosY));
                    else
                    {
                        map.get(entity.getId()).entity = livingEntity;
                        map.get(entity.getId()).leftPoint = posX;
                        map.get(entity.getId()).topPoint = posY;
                        map.get(entity.getId()).rightPoint = endPosX;
                        map.get(entity.getId()).bottomPoint = endPosY;
                    }


                }
            }
        }
    }
}
