package com.leave.ink.features.hud.elements;

import com.leave.ink.Main;
import com.leave.ink.features.hud.AbsHudElement;
import com.leave.ink.features.hud.main.ElementsHud;
import com.leave.ink.features.module.modules.other.NameProtect;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ButtonSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.font.IconFont;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.utils.Utils;
import io.github.humbleui.skija.ClipMode;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.world.scores.*;

import java.awt.Color;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class ScoreboardHud extends AbsHudElement {
    private static final Pattern COLOR_PATTERN = Pattern.compile("(?i)§[0-9A-FK-OR]");

    @SettingInfo(name = { @Text(label = "Background", language = Language.English), @Text(label = "显示背景", language = Language.Chinese) })
    public static final BooleanSetting background = new BooleanSetting(true);

    @SettingInfo(name = { @Text(label = "ShowNumbers", language = Language.English), @Text(label = "显示数字", language = Language.Chinese) })
    public static final BooleanSetting showNumbers = new BooleanSetting(true);

    @SettingInfo(name = { @Text(label = "Delete", language = Language.English), @Text(label = "删除", language = Language.Chinese) })
    public final ButtonSetting delete = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.removeElement(getElementName());
        }
    };

    public ScoreboardHud() {
        super("Scoreboard", 0.85, 0.3, 150, 200);
        registerSetting(background, showNumbers, delete);
    }

    @Override
    protected void processDraw(CanvasStack canvasStack) {
        if (mc.player == null || mc.level == null) return;
        Scoreboard scoreboard = mc.level.getScoreboard();
        Objective objective = scoreboard.getDisplayObjective(1);
        if (objective == null) return;
        List<Score> scoreList = scoreboard.getPlayerScores(objective).stream()
                .filter(score -> !score.getOwner().startsWith("#"))
                .collect(Collectors.toList());

        if (scoreList.size() > 15) {
            scoreList = scoreList.subList(scoreList.size() - 15, scoreList.size());
        }
        if (scoreList.isEmpty()) return;
        String titleText = Utils.getStringFromFormattedCharSequence(objective.getDisplayName().getVisualOrderText());
        float textWidth = getCleanTextWidth(titleText);

        List<ScoreboardEntry> displayList = new ArrayList<>();
        for (Score score : scoreList) {
            String coloredName = Utils.getStringFromFormattedCharSequence(PlayerTeam.formatNameForTeam(scoreboard.getPlayersTeam(score.getOwner()), Component.literal(score.getOwner())).getVisualOrderText());
            NameProtect nameProtect = (NameProtect) Main.INSTANCE.moduleManager.getModule("NameProtect");
            if (nameProtect.isEnable() && mc.player != null && coloredName.contains(mc.player.getName().getString())) {
                coloredName = coloredName.replace(mc.player.getName().getString(), "Hide");
            }

            displayList.add(new ScoreboardEntry(score, coloredName));
            textWidth = Math.max(textWidth, getCleanTextWidth(showNumbers.getValue() ? (coloredName + " " + score.getScore()) : coloredName));
        }
        float width = textWidth + 10;
        float height = 18 + (displayList.size() * 8);
        setWidth(width);
        setHeight(height);

        if (ElementsHud.shadow.getValue()) {
            canvasStack.push();
            SkiaRender.scissorRoundedRect(canvasStack, 0, 0, width, height, 16, ClipMode.DIFFERENCE);
            SkiaRender.drawRoundedRectWithShadow(canvasStack, 0, 0, width, height, 16);
            canvasStack.pop();
        }

        if (background.getValue()) {
            if (ElementsHud.blur.getValue()) {
                SkiaRender.drawBlurRect(canvasStack, 0, 0, width, height, 16, 12);
            }
            SkiaRender.drawRoundedRect(canvasStack, 0, 0, width, height, 16, new Color(24, 24, 24, 120).getRGB());
        }

        float y = 5;
        // 使用统一的颜色文本渲染方法
        float titleX = (float) ((getWidth() - getCleanTextWidth(titleText)) / 2);
        SkiaRender.drawColoredText(canvasStack, titleText, titleX, y, 14, ElementsHud.fontShadow.getValue());
        y += 8;

        for (int i = displayList.size() - 1; i >= 0; i--) {
            ScoreboardEntry entry = displayList.get(i);
            String text = entry.coloredName();
            // 使用统一的颜色文本渲染方法
            SkiaRender.drawColoredText(canvasStack, text, 5, y, 14, ElementsHud.fontShadow.getValue());

            if (showNumbers.getValue()) {
                String score = String.valueOf(entry.score().getScore());
                float scoreX = width - 5 - getCleanTextWidth(score);
                // 使用统一的颜色文本渲染方法，支持图标和颜色混合
                SkiaRender.drawColoredText(canvasStack, score, scoreX, y, 14, ElementsHud.fontShadow.getValue());
            }
            y += 8;
        }
    }



    private float getCleanTextWidth(String text) {
        if (text == null || text.isEmpty()) return 0;

        // 如果包含图标，使用IconFont的宽度计算方法
        if (IconFont.containsIcons(text)) {
            return IconFont.getTextWidthWithIcons(text);
        }

        // 否则使用普通的颜色代码清理方法
        return SkiaFontManager.getDefaultFont14().getWidth(COLOR_PATTERN.matcher(text).replaceAll(""));
    }

    private record ScoreboardEntry(Score score, String coloredName) {}
}