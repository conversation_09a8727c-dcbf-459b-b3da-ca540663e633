package com.leave.ink.injection;

import YeQing.ClassObfuscator;
import com.external.ui.ExternalUI;
import com.leave.ink.injection.base.annotation.*;
import com.leave.ink.injection.base.util.asm.Opcodes;
import com.leave.ink.injection.base.util.asm.Type;
import com.leave.ink.injection.base.util.asm.tree.*;
import com.leave.ink.injection.transformers.client.*;
import com.leave.ink.injection.transformers.entity.EntityRendererTransformer;
import com.leave.ink.injection.transformers.entity.EntityTransformer;
import com.leave.ink.injection.transformers.entity.LivingEntityTransformer;
import com.leave.ink.injection.transformers.fuck.*;
import com.leave.ink.injection.transformers.gui.*;
import com.leave.ink.injection.transformers.heypixel.MixinChatComponentTransformer;
import com.leave.ink.injection.transformers.heypixel.MixinGameRendererTransformer;
import com.leave.ink.injection.transformers.heypixel.MixinMapRendererTransformer;
import com.leave.ink.injection.transformers.input.KeyboardHandlerTransformer;
import com.leave.ink.injection.transformers.input.KeyboardInputTransformer;
import com.leave.ink.injection.transformers.input.MouseHandlerTransformer;
import com.leave.ink.injection.transformers.player.*;
import com.leave.ink.injection.transformers.render.*;
import com.leave.ink.natives.AgentNative;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.InsertPosition;
import com.leave.ink.injection.base.util.Tools;
import javax.xml.transform.TransformerException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

@ClassObfuscator
public class TransformerLoader {
    private final Logger logger = Logger.getLogger("Transformer");
    private final Map<String, Transformer> transformers;

    private ClassLoader getClassLoader() {
        for (Thread thread : Thread.getAllStackTraces().keySet()) {
            if ("Render thread".equalsIgnoreCase(thread.getName())) {
                return thread.getContextClassLoader();
            }
        }
        return null;
    }

    public TransformerLoader() {
        transformers = new HashMap<>();

        //if(getClassLoader() == null) return;

        // HeyPixel
//        if (getClassLoader().getDefinedPackage("com.heypixel.heypixelmod") != null) {
//            addTransformer(new MixinChatComponentTransformer());
//            addTransformer(new MixinGameRendererTransformer());
//            addTransformer(new MixinMapRendererTransformer());
//        }

        // Fuck
        addTransformer(new BufferedReaderTransformer());
        addTransformer(new HashSetTransformer());
        addTransformer(new ReaderTransformer());
        addTransformer(new HashMapTransformer());

        // Transformers
        addTransformer(new MinecraftTransformer());
        addTransformer(new OptionsTransformer());
        addTransformer(new RenderSystemTransformer());
        addTransformer(new TimerTransformer());
        addTransformer(new GameRendererTransformer());
        addTransformer(new EntityRendererTransformer());
        addTransformer(new LivingEntityRendererTransformer());
        addTransformer(new PlayerRendererTransformer());
        addTransformer(new LevelRendererTransformer());
        addTransformer(new ItemInHandRendererTransformer());
        addTransformer(new ItemEntityRendererTransformer());
        addTransformer(new PlayerModelTransformer());
        addTransformer(new HumanoidModelTransformer());
        addTransformer(new EntityTransformer());
        addTransformer(new LivingEntityTransformer());
        addTransformer(new LocalPlayerTransformer());
        addTransformer(new WindowTransformer());
        addTransformer(new GuiTransformer());
        addTransformer(new SpectatorGuiTransformer());
        addTransformer(new PlayerTabOverlayTransformer());
        addTransformer(new ForgeGuiTransformer());
        addTransformer(new KeyboardInputTransformer());
        addTransformer(new KeyboardHandlerTransformer());
        addTransformer(new MouseHandlerTransformer());
        addTransformer(new ClientPacketListenerTransformer());


        addTransformer(new ConnectionTransformer());

        addTransformer(new PacketUtilsTransformer());
        addTransformer(new ClientLevelTransformer());
        addTransformer(new BlockTransformer());
        addTransformer(new MultiPlayerGameModeTransformer());
        addTransformer(new FontTransformer());
        addTransformer(new ChatComponentTransformer());
        addTransformer(new FogRendererTransformer());
        addTransformer(new CapeLayerTransformer());
        addTransformer(new PlayerTransformer());
    }

    private String getBoxedTypeInternalName(Class<?> paramType) {
        if (paramType == int.class) {
            return "java/lang/Integer";
        } else if (paramType == float.class) {
            return "java/lang/Float";
        } else if (paramType == double.class) {
            return "java/lang/Double";
        } else if (paramType == long.class) {
            return "java/lang/Long";
        } else if (paramType == short.class) {
            return "java/lang/Short";
        } else if (paramType == byte.class) {
            return "java/lang/Byte";
        } else if (paramType == char.class) {
            return "java/lang/Character";
        } else if (paramType == boolean.class) {
            return "java/lang/Boolean";
        } else if (paramType == void.class) {
            return "java/lang/Void";
        }
        return Type.getInternalName(paramType);
    }

    public void redefineClass() {
        Set<String> keySet = transformers.keySet();
        int success = 0;
        for (String className : keySet) {
            for (int cnt = 0; cnt < 20; cnt++) {
                try {
                    Class<?> clazz;
                    Transformer transformer = transformers.get(className);
                    ClassTransformer info = transformer.getClass().getAnnotation(ClassTransformer.class);
                    String classNameTransformer;
                    if (info == null) {
                        ClassNameTransformer info2 = transformer.getClass().getAnnotation(ClassNameTransformer.class);
                        if (info2 == null) {
                            classNameTransformer = transformer.className;
                        } else {
                            classNameTransformer = info2.value();
                        }
                        clazz = Class.forName(classNameTransformer);
                    } else {
                        clazz = info.value();
                    }
                    byte[] classByte = AgentNative.getClassBytes(clazz);
                    //byte[] classByte = Tools.getClassBytes(clazz);
                    if (classByte == null) {
                        throw new TransformerException(className + " transformer getClassBytes error");
                    }
                    ClassNode classNode = Tools.getClassNode(classByte);

                    if (classNode == null) {
                        throw new TransformerException(className + " transformer getClassNode error");
                    }

                    Method[] methods = transformer.getClass().getDeclaredMethods();

                    for (Method method : methods) {
                        Overwrite overwrite = method.getAnnotation(Overwrite.class);
                        if (overwrite != null) {
                            method.setAccessible(true);
                            String desc = overwrite.desc();
                            MethodNode methodNode = null;
                            for (String name : overwrite.methodName()) {
                                methodNode = Tools.getMethod(classNode, desc, name);
                                if (methodNode != null) break;
                            }
                            if (methodNode != null) {
                                PushArgs pushArgs = method.getAnnotation(PushArgs.class);
                                InsnList list = new InsnList();
                                if (pushArgs == null) {
                                    Class<?>[] paramTypes = method.getParameterTypes();
                                    Type[] asmParamTypes = new Type[paramTypes.length];
                                    for (int i = 0; i < paramTypes.length; i++) {
                                        asmParamTypes[i] = Type.getType(paramTypes[i]);
                                    }
                                    list.add(new VarInsnNode(Opcodes.ALOAD, 0));
                                    for (int i = 1; i < asmParamTypes.length; i++) {
                                        Type paramType = asmParamTypes[i];
                                        System.out.println("loaded " + paramType.getOpcode(Opcodes.ILOAD) + " " + (i));
                                        list.add(new VarInsnNode(paramType.getOpcode(Opcodes.ILOAD), i));
                                    }
                                } else {
                                    if (pushArgs.index().length != pushArgs.opcode().length) {
                                        throw new TransformerException("参数错误");
                                    }
                                    for (int index = 0; index < pushArgs.index().length; ++index) {
                                        list.add(new VarInsnNode(pushArgs.opcode()[index], pushArgs.index()[index]));
                                    }
                                }

                                list.add(new MethodInsnNode(
                                        Opcodes.INVOKESTATIC,
                                        Type.getInternalName(transformer.getClass()),
                                        method.getName(),
                                        Type.getMethodDescriptor(method)
                                ));

                                switch (getBoxedTypeInternalName(method.getReturnType())) {
                                    case "java/lang/Integer", "java/lang/Boolean" -> list.add(new InsnNode(Opcodes.IRETURN));
                                    case "java/lang/Float" -> list.add(new InsnNode(Opcodes.FRETURN));
                                    case "java/lang/Double" -> list.add(new InsnNode(Opcodes.DRETURN));
                                    default -> list.add(new InsnNode(Opcodes.RETURN));
                                }

                                methodNode.instructions.insert(methodNode.instructions.getFirst(), list);
                            }
                        }
                    }

                    int stack = 0;
                    for (Method method : methods) {
                        ASM asm = method.getAnnotation(ASM.class);
                        PushArgs pushArgs = method.getAnnotation(PushArgs.class);
                        Inject inject = method.getAnnotation(Inject.class);
                        InjectPoint injectPoint = method.getAnnotation(InjectPoint.class);
                        Store store = method.getAnnotation(Store.class);
                        if (injectPoint != null && inject == null && asm == null) {
                            throw new Exception("Can't found Inject annotation");
                        }
                        if (inject != null) {
                            method.setAccessible(true);
                            String desc = inject.desc();
                            MethodNode methodNode = null;

                            for (String name : inject.methodName()) {
                                methodNode = Tools.getMethod(classNode, desc, name);
                                if (methodNode != null) break;
                            }
                            if (methodNode != null) {
                                inject.at().setMethodNode(methodNode);
                                InsnList list = new InsnList();
                                if (inject.callback().callback()) {
                                    list.add(new MethodInsnNode(
                                            Opcodes.INVOKESTATIC,
                                            Type.getInternalName(getClass()),
                                            "getCallBackInfo",
                                            "()L" + Type.getInternalName(CallBackInfo.class) + ";",
                                            false
                                    ));
                                }
                                if (pushArgs != null) {
                                    if (pushArgs.index().length != pushArgs.opcode().length) {
                                        throw new TransformerException("参数错误");
                                    }
                                    for (int index = 0; index < pushArgs.index().length; ++index) {
                                        list.add(new VarInsnNode(pushArgs.opcode()[index], pushArgs.index()[index]));
                                    }
                                }
                                methodNode.maxStack += 1;
                                methodNode.maxLocals += 1;
                                stack = methodNode.maxLocals;
                                int localVariableIndex = methodNode.maxLocals;

                                if (inject.callback().callback()) {
                                    list.add(new InsnNode(Opcodes.ACONST_NULL));
                                    list.add(new VarInsnNode(Opcodes.ASTORE, localVariableIndex));
                                }

                                list.add(new MethodInsnNode(
                                        Opcodes.INVOKESTATIC,
                                        Type.getInternalName(transformer.getClass()),
                                        method.getName(),
                                        Type.getMethodDescriptor(method)
                                ));

                                if (store != null) {
                                    for (int index = 0; index < store.index().length; ++index) {
                                        list.add(new VarInsnNode(store.opcode()[index], store.index()[index]));
                                    }
                                }

                                if (inject.callback().callback())
                                    list.add(new VarInsnNode(Opcodes.ASTORE, localVariableIndex));

                                if (inject.callback().callback()) {
                                    list.add(new VarInsnNode(Opcodes.ALOAD, localVariableIndex));
                                    list.add(new MethodInsnNode(
                                            Opcodes.INVOKEVIRTUAL,
                                            Type.getInternalName(CallBackInfo.class),
                                            "isBack",
                                            "()Z"
                                    ));
                                    LabelNode label = new LabelNode();
                                    list.add(new JumpInsnNode(Opcodes.IFEQ, label));

                                    if (!getBoxedTypeInternalName(inject.callback().type()).equals("java/lang/Void")) {
                                        list.add(new VarInsnNode(Opcodes.ALOAD, localVariableIndex));
                                        list.add(new MethodInsnNode(
                                                Opcodes.INVOKEVIRTUAL,
                                                Type.getInternalName(CallBackInfo.class),
                                                "getBackValue",
                                                "()Ljava/lang/Object;"
                                        ));
                                        switch (getBoxedTypeInternalName(inject.callback().type())) {
                                            case "java/lang/Integer": {
                                                list.add(new MethodInsnNode(
                                                        Opcodes.INVOKESTATIC,
                                                        Type.getInternalName(Tools.class),
                                                        "castToInteger",
                                                        "(Ljava/lang/Object;)I"
                                                ));
                                                list.add(new InsnNode(Opcodes.IRETURN));
                                                break;
                                            }
                                            case "java/lang/Float": {
                                                list.add(new MethodInsnNode(
                                                        Opcodes.INVOKESTATIC,
                                                        Type.getInternalName(Tools.class),
                                                        "castToFloat",
                                                        "(Ljava/lang/Object;)F"
                                                ));
                                                list.add(new InsnNode(Opcodes.FRETURN));
                                                break;
                                            }
                                            case "java/lang/Double": {
                                                list.add(new MethodInsnNode(
                                                        Opcodes.INVOKESTATIC,
                                                        Type.getInternalName(Tools.class),
                                                        "castToDouble",
                                                        "(Ljava/lang/Object;)D"
                                                ));
                                                list.add(new InsnNode(Opcodes.DRETURN));
                                                break;
                                            }
                                            case "java/lang/Boolean": {
                                                list.add(new MethodInsnNode(
                                                        Opcodes.INVOKESTATIC,
                                                        Type.getInternalName(Tools.class),
                                                        "castToBoolean",
                                                        "(Ljava/lang/Object;)Z"
                                                ));
                                                list.add(new InsnNode(Opcodes.IRETURN));
                                                break;
                                            }
                                            default: {
                                                list.add(new InsnNode(Opcodes.ARETURN));
                                                break;
                                            }
                                        }
                                    } else {
                                        list.add(new InsnNode(Opcodes.RETURN));
                                    }
                                    list.add(label);
                                }

                                InsertPosition point = inject.at();
                                AbstractInsnNode insertPoint = null;
                                if (inject.at() == InsertPosition.STR) {
                                    if (injectPoint == null) {
                                        throw new TransformerException("injectPoint为空");
                                    }
                                    for (String s : injectPoint.methodName()) {
                                        insertPoint = Tools.getPoint(methodNode, s);
                                        if (insertPoint != null) break;
                                    }
                                    if (insertPoint == null) {
                                        logger.log(Level.WARNING, "未找到注入点 " + transformer.getClass().getName());
                                    }
                                } else {
                                    insertPoint = inject.at().getPosition(point);
                                    if (insertPoint == null && injectPoint != null) {
                                        insertPoint = Tools.getPoint(methodNode, injectPoint.index(), injectPoint.desc(), injectPoint.methodName());
                                    }
                                }

                                if (insertPoint != null) {
                                    inject.at().insert(insertPoint, list);
                                } else {
                                    logger.log(Level.WARNING, "未找到注入点 " + transformer.getClass().getName() + " " + method.getName());
                                }
                            }
                        }
                        if (asm != null) {
                            method.setAccessible(true);
                            String desc = asm.desc();
                            MethodNode methodNode = null;
                            for (String name : asm.methodName()) {
                                methodNode = Tools.getMethod(classNode, desc, name);
                                if (methodNode != null) break;
                            }
                            if (methodNode != null) {
                                InsnList list = new InsnList();
                                CallBackInfo callBackInfo = new CallBackInfo();
                                method.invoke(transformer, list, methodNode, callBackInfo);
                            }
                        }
                    }

                    byte[] newClassByte = Tools.rewriteClass(classNode);
                    if(clazz.equals(HashSet.class) || clazz.equals(HashMap.class)) {
                        try (FileOutputStream fos = new FileOutputStream(clazz.getName() + ".class")) {
                            fos.write(newClassByte);
                        } catch (IOException ignored) {}
                    }
                    int errorCode = AgentNative.RedefineClass(clazz, newClassByte);
                    if (errorCode != 0) {
                        throw new TransformerException(className + " transformer RedefineClass error " + errorCode);
                    }
                    System.out.println(className + " transformer successful [count=" + cnt + "][stack=" + stack + "]");

                    success++;
                    break;
                } catch (Exception e) {
                    if (cnt == 1) {
                        System.out.println("错误 " + e);
                        ExternalUI.log("Transform Error [" + className.replace(".","/") + "] " + e);
                    }
                }
            }
        }

        ExternalUI.log("[Transformer]Success " + success + " Error " + (transformers.size() - success));
        System.out.println("[Transformer]Success " + success + " Error " + (transformers.size() - success));
    }

    public static CallBackInfo getCallBackInfo() {
        return new CallBackInfo();
    }

    public void addTransformer(Transformer... transformers) {
        Arrays.stream(transformers).forEach(transformer -> {
            ClassTransformer info = transformer.getClass().getAnnotation(ClassTransformer.class);
            if (info == null) {
                ClassNameTransformer info2 = transformer.getClass().getAnnotation(ClassNameTransformer.class);
                if (info2 == null) {
                    this.transformers.put(transformer.className, transformer);
                } else {
                    this.transformers.put(info2.value(), transformer);
                }
            } else {
                this.transformers.put(info.value().getName(), transformer);
            }
        });
    }
}
